"use client";

import { motion, AnimatePresence } from "framer-motion";
import { Target, X } from "lucide-react";
import PracticalScenario, { examplePracticalScenarioData } from "@/components/ui/learning/PracticalScenario";

interface PracticalScenarioPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  conceptTitle: string;
}

export default function PracticalScenarioPreviewModal({
  isOpen,
  onClose,
  conceptTitle,
}: PracticalScenarioPreviewModalProps) {
  const handleClose = () => {
    onClose();
  };

  const currentData = examplePracticalScenarioData[0];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="practical-scenario-preview-modal"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[60] flex items-center justify-center p-4"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        >
          <motion.div
            key="practical-scenario-preview-content"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="relative w-full max-w-5xl max-h-[90vh] overflow-hidden rounded-2xl"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div
              className="px-8 py-6 border-b"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div
                    className="p-3 rounded-lg"
                    style={{ backgroundColor: "var(--charcoal)" }}
                  >
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2
                      className="text-2xl font-bold"
                      style={{ color: "var(--charcoal)" }}
                    >
                      Practical Scenario Preview
                    </h2>
                    <p
                      className="text-sm mt-1"
                      style={{ color: "var(--grey)" }}
                    >
                      {conceptTitle} • Interactive scenario walkthrough
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{ 
                    backgroundColor: "var(--color-muted)",
                    color: "var(--grey)"
                  }}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div
              className="p-8 overflow-y-auto"
              style={{
                backgroundColor: "var(--color-muted)",
                maxHeight: "calc(90vh - 200px)"
              }}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <PracticalScenario
                  data={currentData}
                  onStepComplete={(stepNumber) => {
                    console.log(`Practical Scenario step completed:`, stepNumber);
                  }}
                  onScenarioComplete={() => {
                    console.log(`Practical Scenario completed`);
                  }}
                />
              </motion.div>

              {/* Category Display */}
              <div className="mt-8 flex justify-center">
                <div
                  className="px-4 py-2 rounded-lg text-sm font-medium inline-block"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                >
                  {currentData.category}
                </div>
              </div>

              {/* Preview Note */}
              <div className="mt-6 text-center">
                <p
                  className="text-sm"
                  style={{ color: "var(--grey)" }}
                >
                  This is a preview of the Practical Scenario learning method. 
                  In the actual learning session, scenarios will be tailored to your specific topic.
                </p>
              </div>
            </div>

            {/* Footer */}
            <div
              className="px-8 py-6 border-t"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="text-sm" style={{ color: "var(--grey)" }}>
                  Interactive scenario with step-by-step guidance and hints
                </div>
                <button
                  onClick={handleClose}
                  className="px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-[1.02]"
                  style={{
                    backgroundColor: "var(--emerald)",
                    color: "var(--white)"
                  }}
                >
                  Got it
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
