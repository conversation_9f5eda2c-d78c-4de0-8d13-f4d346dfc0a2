"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { Eye } from "lucide-react";
import FlashcardPreviewModal from "./FlashcardPreviewModal";
import TrueFalsePreviewModal from "./TrueFalsePreviewModal";
import TwoTruthsOneFakePreviewModal from "./TwoTruthsOneFakePreviewModal";
import MentorMePreviewModal from "./MentorMePreviewModal";
import PracticalScenarioPreviewModal from "./PracticalScenarioPreviewModal";

interface LearningMethod {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

interface LearningMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStart: (selectedMethods: string[]) => void;
  conceptTitle: string;
}

const learningMethods: LearningMethod[] = [
  {
    id: "flashcards",
    name: "Flashcards",
    description: "Quick review with interactive cards",
    color: "var(--emerald)",
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path key="flashcards-icon" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
    ),
  },
  {
    id: "true-false",
    name: "True or False",
    description: "Test your knowledge with binary choices",
    color: "var(--bright-green)",
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path key="true-false-icon" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: "two-truths-one-fake",
    name: "2 Truths 1 Fake",
    description: "Spot the incorrect statement",
    color: "var(--lime-green)",
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path key="two-truths-icon" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: "mentor-me",
    name: "Mentor Me",
    description: "AI-guided personalized learning",
    color: "var(--emerald-deep)",
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path key="mentor-icon-1" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0z" />
        <path key="mentor-icon-2" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    ),
  },
  {
    id: "practical-scenario",
    name: "Practical Scenario Walkthrough",
    description: "Real-world application scenarios",
    color: "var(--charcoal)",
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path key="practical-icon" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    ),
  },
];

export default function LearningMethodModal({
  isOpen,
  onClose,
  onStart,
  conceptTitle,
}: LearningMethodModalProps) {
  const [selectedMethods, setSelectedMethods] = useState<string[]>([]);
  const [showFlashcardPreview, setShowFlashcardPreview] = useState(false);
  const [showTrueFalsePreview, setShowTrueFalsePreview] = useState(false);
  const [showTwoTruthsPreview, setShowTwoTruthsPreview] = useState(false);
  const [showMentorMePreview, setShowMentorMePreview] = useState(false);
  const [showPracticalScenarioPreview, setShowPracticalScenarioPreview] = useState(false);

  const toggleMethod = (methodId: string) => {
    setSelectedMethods(prev => 
      prev.includes(methodId)
        ? prev.filter(id => id !== methodId)
        : [...prev, methodId]
    );
  };

  const handleStart = () => {
    if (selectedMethods.length > 0) {
      onStart(selectedMethods);
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedMethods([]);
    setShowFlashcardPreview(false);
    setShowTrueFalsePreview(false);
    setShowTwoTruthsPreview(false);
    setShowMentorMePreview(false);
    setShowPracticalScenarioPreview(false);
    onClose();
  };

  const handlePreviewFlashcard = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowFlashcardPreview(true);
  };

  const handlePreviewTrueFalse = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowTrueFalsePreview(true);
  };

  const handlePreviewTwoTruths = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowTwoTruthsPreview(true);
  };

  const handlePreviewMentorMe = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowMentorMePreview(true);
  };

  const handlePreviewPracticalScenario = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowPracticalScenarioPreview(true);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="learning-method-modal"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        >
          <motion.div
            key="learning-method-content"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden rounded-2xl"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div
              className="px-8 py-6 border-b"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 relative">
                    <Image
                      src="/images/dtc-logo.png"
                      alt="DTC Logo"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <div>
                    <h2
                      className="text-2xl font-bold"
                      style={{ color: "var(--charcoal)" }}
                    >
                      How do you want to learn?
                    </h2>
                    <p
                      className="text-sm mt-1"
                      style={{ color: "var(--grey)" }}
                    >
                      {conceptTitle} • Choose your learning methods
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{ 
                    backgroundColor: "var(--color-muted)",
                    color: "var(--grey)"
                  }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {learningMethods.map((method) => (
                  <motion.div
                    key={method.id}
                    className={`relative p-6 rounded-xl text-left transition-all duration-200 hover:scale-[1.02] cursor-pointer ${
                      selectedMethods.includes(method.id)
                        ? 'ring-2 shadow-lg'
                        : 'hover:shadow-md'
                    }`}
                    style={{
                      backgroundColor: selectedMethods.includes(method.id)
                        ? `${method.color}15`
                        : "var(--color-muted)",
                      borderColor: selectedMethods.includes(method.id)
                        ? method.color
                        : "var(--color-border)",
                      ringColor: selectedMethods.includes(method.id)
                        ? method.color
                        : "transparent",
                    }}
                    whileHover={{ y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => toggleMethod(method.id)}
                  >
                    <div className="flex items-start gap-4">
                      <div
                        className="p-3 rounded-lg flex-shrink-0"
                        style={{
                          backgroundColor: method.color,
                          color: "var(--white)"
                        }}
                      >
                        {method.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3
                            className="font-semibold text-sm"
                            style={{ color: "var(--charcoal)" }}
                          >
                            {method.name}
                          </h3>
                          {(method.id === "flashcards" || method.id === "true-false" || method.id === "two-truths-one-fake" || method.id === "mentor-me" || method.id === "practical-scenario") && (
                            <button
                              onClick={
                                method.id === "flashcards" ? handlePreviewFlashcard :
                                method.id === "true-false" ? handlePreviewTrueFalse :
                                method.id === "two-truths-one-fake" ? handlePreviewTwoTruths :
                                method.id === "mentor-me" ? handlePreviewMentorMe :
                                method.id === "practical-scenario" ? handlePreviewPracticalScenario :
                                undefined
                              }
                              className="p-1 rounded transition-colors duration-200 hover:bg-white/20 z-10 relative"
                              style={{ color: "var(--emerald)" }}
                              title={`Preview ${method.name.toLowerCase()}`}
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        <p
                          className="text-xs leading-relaxed"
                          style={{ color: "var(--grey)" }}
                        >
                          {method.description}
                        </p>
                      </div>
                      {selectedMethods.includes(method.id) && (
                        <motion.div
                          key={`selected-${method.id}`}
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="flex-shrink-0"
                        >
                          <div
                            className="w-5 h-5 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: method.color }}
                          >
                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path key={`check-${method.id}`} strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div
              className="px-8 py-6 border-t"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="text-sm" style={{ color: "var(--grey)" }}>
                  {selectedMethods.length > 0 
                    ? `${selectedMethods.length} method${selectedMethods.length > 1 ? 's' : ''} selected`
                    : "Select at least one learning method to continue"
                  }
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleClose}
                    className="px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200"
                    style={{
                      backgroundColor: "var(--color-muted)",
                      color: "var(--charcoal)"
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleStart}
                    disabled={selectedMethods.length === 0}
                    className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                      selectedMethods.length > 0 
                        ? 'hover:scale-[1.02] hover:shadow-lg' 
                        : 'opacity-50 cursor-not-allowed'
                    }`}
                    style={{
                      backgroundColor: selectedMethods.length > 0 ? "var(--emerald)" : "var(--grey)",
                      color: "var(--white)"
                    }}
                  >
                    Start Learning
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Preview Modals */}
      <FlashcardPreviewModal
        isOpen={showFlashcardPreview}
        onClose={() => setShowFlashcardPreview(false)}
        conceptTitle={conceptTitle}
      />

      <TrueFalsePreviewModal
        isOpen={showTrueFalsePreview}
        onClose={() => setShowTrueFalsePreview(false)}
        conceptTitle={conceptTitle}
      />

      <TwoTruthsOneFakePreviewModal
        isOpen={showTwoTruthsPreview}
        onClose={() => setShowTwoTruthsPreview(false)}
        conceptTitle={conceptTitle}
      />

      <MentorMePreviewModal
        isOpen={showMentorMePreview}
        onClose={() => setShowMentorMePreview(false)}
        conceptTitle={conceptTitle}
      />

      <PracticalScenarioPreviewModal
        isOpen={showPracticalScenarioPreview}
        onClose={() => setShowPracticalScenarioPreview(false)}
        conceptTitle={conceptTitle}
      />
    </AnimatePresence>
  );
}
