# Learning Components

A comprehensive set of interactive learning components built with React, TypeScript, and Framer Motion, following DTC's design system.

## Features

- ✨ **Smooth Animations**: Beautiful animations powered by Framer Motion
- 🎨 **DTC Color Scheme**: Consistent with DTC brand colors and design system
- 📱 **Responsive Design**: Works perfectly on all screen sizes and devices
- ♿ **Accessible**: Full keyboard navigation and screen reader support
- 🔧 **TypeScript Ready**: Fully typed with comprehensive interfaces
- 📚 **Learning Focused**: Designed specifically for educational content
- 👁️ **Preview Integration**: Eye icon preview in learning method selection
- 🎯 **Multiple Methods**: Flashcards, True/False, 2 Truths 1 Fake, and Guided Learning

## Components

### `Flashcard`
Interactive flashcard component with flip animation.

```tsx
import { Flashcard } from '@/components/ui/learning';

<Flashcard
  data={flashcardData}
  onFlip={(isFlipped) => console.log('Flipped:', isFlipped)}
/>
```

### `TrueFalse`
True or False question component with explanations.

```tsx
import { TrueFalse } from '@/components/ui/learning';

<TrueFalse
  data={trueFalseData}
  onAnswer={(isCorrect, selectedAnswer) => console.log('Answer:', isCorrect)}
/>
```

### `TwoTruthsOneFake`
Interactive component to identify the false statement among three options.

```tsx
import { TwoTruthsOneFake } from '@/components/ui/learning';

<TwoTruthsOneFake
  data={twoTruthsData}
  onAnswer={(isCorrect, selectedIndex) => console.log('Answer:', isCorrect)}
/>
```

### `MentorMe`
Guided learning component with AI mentor conversation flow and rich content support.

**Supported Content Types:**
- **Tables**: Structured data presentation
- **Lists**: Bullet points with checkmarks
- **Comparisons**: Side-by-side pros/cons
- **Steps**: Numbered process flows
- **Highlights**: Important key takeaways
- **Quiz**: Interactive knowledge checks
- **Progress**: Visual skill progress bars
- **Diagrams**: Flow charts and processes
- **Tips**: Pro tips with icons and descriptions

```tsx
import { MentorMe } from '@/components/ui/learning';

<MentorMe
  data={mentorData}
  onInteraction={(message) => console.log('User message:', message)}
/>
```

### Preview Modals
Each learning method has a corresponding preview modal that shows when clicking the eye icon in learning method selection.

## Props

### FlashcardData Interface

```typescript
interface FlashcardData {
  id: string;
  question: string;
  answer: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}
```

### Flashcard Props

```typescript
interface FlashcardProps {
  data: FlashcardData;
  className?: string;
  onFlip?: (isFlipped: boolean) => void;
  autoFlip?: boolean;
}
```

### PracticalScenarioData Interface

```typescript
interface PracticalScenarioData {
  id: string;
  title: string;
  context: string;
  challenge: string;
  steps: {
    step: number;
    description: string;
    hint?: string;
  }[];
  expectedOutcome: string;
  keyLearnings: string[];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}
```

### RealImplementationData Interface

```typescript
interface RealImplementationData {
  id: string;
  title: string;
  description: string;
  realWorldContext: string;
  implementationSteps: {
    step: number;
    title: string;
    description: string;
    codeExample?: string;
    language?: string;
    tools?: string[];
    bestPractices?: string[];
  }[];
  projectStructure?: {
    files: string[];
    description: string;
  };
  commonPitfalls?: string[];
  nextSteps?: string[];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}
```

## Size

- **Large**: 384×224px (fixed size for consistency)

## Difficulty Colors

- **Easy**: Bright Green (`var(--bright-green)`)
- **Medium**: Lime Green (`var(--lime-green)`)
- **Hard**: Deep Emerald (`var(--emerald-deep)`)

## Integration

The flashcard component is integrated into the learning method selection modal. When users click "Start Learning" on a concept and select "Flashcards" as a learning method, they can click the eye icon (👁️) to preview the flashcard component with a general example.

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- ARIA labels and roles
- Focus management
- High contrast support

## DTC Design System

The component follows DTC's design guidelines:
- Uses semantic color tokens from `globals.css`
- Consistent typography with Montserrat/Glancyr fonts
- Elegant borders and shadows
- No black borders or harsh contrasts
- Smooth, subtle animations
