"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle, Circle, Lightbulb, Target, BookOpen, ArrowRight } from "lucide-react";

export interface PracticalScenarioData {
  id: string;
  title: string;
  context: string;
  challenge: string;
  steps: {
    step: number;
    description: string;
    hint?: string;
  }[];
  expectedOutcome: string;
  keyLearnings: string[];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface PracticalScenarioProps {
  data: PracticalScenarioData;
  className?: string;
  onStepComplete?: (stepNumber: number) => void;
  onScenarioComplete?: () => void;
}

export default function PracticalScenario({
  data,
  className = "",
  onStepComplete,
  onScenarioComplete,
}: PracticalScenarioProps) {
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [showHints, setShowHints] = useState<number[]>([]);
  const [currentStep, setCurrentStep] = useState(1);

  const handleStepToggle = (stepNumber: number) => {
    const isCompleted = completedSteps.includes(stepNumber);
    
    if (isCompleted) {
      setCompletedSteps(prev => prev.filter(step => step !== stepNumber));
    } else {
      setCompletedSteps(prev => [...prev, stepNumber]);
      onStepComplete?.(stepNumber);
      
      // Auto-advance to next step
      if (stepNumber === currentStep && stepNumber < data.steps.length) {
        setCurrentStep(stepNumber + 1);
      }
      
      // Check if all steps are completed
      if (completedSteps.length + 1 === data.steps.length) {
        onScenarioComplete?.();
      }
    }
  };

  const toggleHint = (stepNumber: number) => {
    setShowHints(prev => 
      prev.includes(stepNumber)
        ? prev.filter(step => step !== stepNumber)
        : [...prev, stepNumber]
    );
  };

  const getDifficultyColor = () => {
    switch (data.difficulty) {
      case "easy": return "var(--bright-green)";
      case "medium": return "var(--emerald)";
      case "hard": return "var(--charcoal)";
      default: return "var(--emerald)";
    }
  };

  const isStepCompleted = (stepNumber: number) => completedSteps.includes(stepNumber);
  const isStepCurrent = (stepNumber: number) => stepNumber === currentStep;

  return (
    <div 
      className={`w-full max-w-4xl mx-auto p-6 rounded-2xl ${className}`}
      style={{ 
        backgroundColor: "var(--white)",
        border: "1px solid var(--color-border)"
      }}
    >
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-3">
          <div 
            className="p-2 rounded-lg"
            style={{ backgroundColor: getDifficultyColor() }}
          >
            <Target className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 
              className="text-xl font-bold"
              style={{ color: "var(--charcoal)" }}
            >
              {data.title}
            </h2>
            {data.difficulty && (
              <span 
                className="text-xs px-2 py-1 rounded-full font-medium"
                style={{ 
                  backgroundColor: `${getDifficultyColor()}20`,
                  color: getDifficultyColor()
                }}
              >
                {data.difficulty.charAt(0).toUpperCase() + data.difficulty.slice(1)}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Context Section */}
      <div 
        className="p-4 rounded-xl mb-6"
        style={{ backgroundColor: "var(--color-muted)" }}
      >
        <div className="flex items-start gap-3">
          <BookOpen 
            className="w-5 h-5 mt-1 flex-shrink-0"
            style={{ color: "var(--emerald)" }}
          />
          <div>
            <h3 
              className="font-semibold mb-2"
              style={{ color: "var(--charcoal)" }}
            >
              Scenario Context
            </h3>
            <p 
              className="text-sm leading-relaxed"
              style={{ color: "var(--grey)" }}
            >
              {data.context}
            </p>
          </div>
        </div>
      </div>

      {/* Challenge Section */}
      <div 
        className="p-4 rounded-xl mb-6"
        style={{ 
          backgroundColor: "var(--emerald)",
          color: "var(--white)"
        }}
      >
        <h3 className="font-semibold mb-2">Your Challenge</h3>
        <p className="text-sm leading-relaxed opacity-90">
          {data.challenge}
        </p>
      </div>

      {/* Steps Section */}
      <div className="mb-6">
        <h3 
          className="font-semibold mb-4 flex items-center gap-2"
          style={{ color: "var(--charcoal)" }}
        >
          <ArrowRight className="w-4 h-4" />
          Implementation Steps
        </h3>
        
        <div className="space-y-3">
          {data.steps.map((step) => (
            <motion.div
              key={step.step}
              className={`p-4 rounded-xl border transition-all duration-200 cursor-pointer ${
                isStepCurrent(step.step) ? 'ring-2' : ''
              }`}
              style={{
                backgroundColor: isStepCompleted(step.step) 
                  ? "var(--bright-green)20" 
                  : isStepCurrent(step.step)
                  ? "var(--emerald)10"
                  : "var(--white)",
                borderColor: isStepCompleted(step.step)
                  ? "var(--bright-green)"
                  : isStepCurrent(step.step)
                  ? "var(--emerald)"
                  : "var(--color-border)",
                ringColor: isStepCurrent(step.step) ? "var(--emerald)" : "transparent"
              }}
              onClick={() => handleStepToggle(step.step)}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
            >
              <div className="flex items-start gap-3">
                <button className="flex-shrink-0 mt-1">
                  {isStepCompleted(step.step) ? (
                    <CheckCircle 
                      className="w-5 h-5"
                      style={{ color: "var(--bright-green)" }}
                    />
                  ) : (
                    <Circle 
                      className="w-5 h-5"
                      style={{ color: "var(--grey)" }}
                    />
                  )}
                </button>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span 
                      className="text-xs font-medium px-2 py-1 rounded-full"
                      style={{ 
                        backgroundColor: "var(--emerald)",
                        color: "var(--white)"
                      }}
                    >
                      Step {step.step}
                    </span>
                  </div>
                  
                  <p 
                    className="text-sm leading-relaxed"
                    style={{ color: "var(--charcoal)" }}
                  >
                    {step.description}
                  </p>
                  
                  {step.hint && (
                    <div className="mt-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleHint(step.step);
                        }}
                        className="flex items-center gap-2 text-xs font-medium transition-colors duration-200"
                        style={{ color: "var(--emerald)" }}
                      >
                        <Lightbulb className="w-4 h-4" />
                        {showHints.includes(step.step) ? "Hide Hint" : "Show Hint"}
                      </button>
                      
                      <AnimatePresence>
                        {showHints.includes(step.step) && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            className="mt-2 p-3 rounded-lg"
                            style={{ 
                              backgroundColor: "var(--emerald)10",
                              border: "1px solid var(--emerald)30"
                            }}
                          >
                            <p 
                              className="text-xs leading-relaxed"
                              style={{ color: "var(--grey)" }}
                            >
                              💡 {step.hint}
                            </p>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Expected Outcome */}
      <div 
        className="p-4 rounded-xl mb-6"
        style={{ backgroundColor: "var(--color-muted)" }}
      >
        <h3 
          className="font-semibold mb-2"
          style={{ color: "var(--charcoal)" }}
        >
          Expected Outcome
        </h3>
        <p 
          className="text-sm leading-relaxed"
          style={{ color: "var(--grey)" }}
        >
          {data.expectedOutcome}
        </p>
      </div>

      {/* Key Learnings */}
      <div>
        <h3 
          className="font-semibold mb-3"
          style={{ color: "var(--charcoal)" }}
        >
          Key Learnings
        </h3>
        <div className="grid gap-2">
          {data.keyLearnings.map((learning, index) => (
            <div 
              key={index}
              className="flex items-start gap-3 p-3 rounded-lg"
              style={{ backgroundColor: "var(--bright-green)10" }}
            >
              <CheckCircle 
                className="w-4 h-4 mt-0.5 flex-shrink-0"
                style={{ color: "var(--bright-green)" }}
              />
              <p 
                className="text-sm leading-relaxed"
                style={{ color: "var(--charcoal)" }}
              >
                {learning}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mt-6 pt-4 border-t" style={{ borderColor: "var(--color-border)" }}>
        <div className="flex items-center justify-between text-sm">
          <span style={{ color: "var(--grey)" }}>
            Progress: {completedSteps.length} of {data.steps.length} steps completed
          </span>
          <div 
            className="h-2 w-32 rounded-full overflow-hidden"
            style={{ backgroundColor: "var(--color-muted)" }}
          >
            <motion.div
              className="h-full rounded-full"
              style={{ backgroundColor: "var(--emerald)" }}
              initial={{ width: 0 }}
              animate={{ 
                width: `${(completedSteps.length / data.steps.length) * 100}%` 
              }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Example data for preview
export const examplePracticalScenarioData: PracticalScenarioData[] = [
  {
    id: "example-scenario-1",
    title: "Implementing Data Privacy Controls",
    context: "You are a Data Protection Officer at a mid-sized e-commerce company. The company has recently expanded to serve EU customers and needs to ensure GDPR compliance for personal data processing.",
    challenge: "Design and implement a comprehensive data privacy framework that ensures lawful processing of customer data while maintaining business operations.",
    steps: [
      {
        step: 1,
        description: "Conduct a data mapping exercise to identify all personal data flows within the organization.",
        hint: "Start by mapping customer data from collection points (website forms, checkout) through processing systems to storage locations."
      },
      {
        step: 2,
        description: "Establish legal bases for processing under GDPR Article 6 for each data processing activity.",
        hint: "Consider consent, contract performance, legitimate interests, and legal obligations as potential legal bases."
      },
      {
        step: 3,
        description: "Implement privacy by design principles in your data processing systems.",
        hint: "Focus on data minimization, purpose limitation, and built-in privacy controls from the system design phase."
      },
      {
        step: 4,
        description: "Create and deploy privacy notices and consent mechanisms for data subjects.",
        hint: "Ensure notices are clear, concise, and easily accessible. Implement granular consent options where required."
      },
      {
        step: 5,
        description: "Establish procedures for handling data subject rights requests (access, rectification, erasure).",
        hint: "Create standardized workflows and response templates to ensure timely compliance with rights requests."
      }
    ],
    expectedOutcome: "A fully compliant GDPR framework with documented processes, implemented technical controls, and trained staff capable of maintaining ongoing compliance.",
    keyLearnings: [
      "Data mapping is essential for understanding privacy risks and compliance requirements",
      "Legal bases must be carefully selected and documented for each processing activity",
      "Privacy by design reduces compliance costs and improves data protection outcomes",
      "Clear communication with data subjects builds trust and reduces compliance risks",
      "Systematic approaches to rights management ensure consistent and timely responses"
    ],
    category: "Data Protection",
    difficulty: "medium",
    tags: ["GDPR", "Privacy", "Compliance", "Data Protection"]
  }
];
